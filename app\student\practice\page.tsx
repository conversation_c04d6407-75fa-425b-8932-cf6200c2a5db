"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Play, 
  Clock, 
  Target, 
  Zap,
  BookOpen,
  TrendingUp,
  Calendar,
  Award,
  CheckCircle,
  Star,
  Brain,
  Timer,
  RotateCcw
} from "lucide-react"

import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import Link from "next/link"
import { CategoryFilter } from "@/components/admin/category-filter"

interface PracticeSession {
  id: string
  title: string
  description: string
  category: string
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  questionCount: number
  estimatedTime: number
  points: number
  isCompleted: boolean
  completedAt?: string
  score?: number
  streak: number
}

interface DailyStats {
  currentStreak: number
  longestStreak: number
  totalSessions: number
  totalPoints: number
  averageScore: number
  todayCompleted: number
  todayTarget: number
}

export default function DailyPractice() {
  const [practiceData, setPracticeData] = useState<PracticeSession[]>([])
  const [stats, setStats] = useState<DailyStats>({
    currentStreak: 0,
    longestStreak: 0,
    totalSessions: 0,
    totalPoints: 0,
    averageScore: 0,
    todayCompleted: 0,
    todayTarget: 5
  })
  const [loading, setLoading] = useState(true)
  const [categoryFilters, setCategoryFilters] = useState<{
    subjectId?: string
    chapterId?: string
    topicId?: string
  }>({})
  const [resetting, setResetting] = useState(false)

  useEffect(() => {
    fetchPracticeData()
  }, [categoryFilters])

  // Debug: Log category filters
  useEffect(() => {
    console.log('Daily Practice - Category filters changed:', categoryFilters)
  }, [categoryFilters])

  const fetchPracticeData = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (categoryFilters.subjectId) params.append('subjectId', categoryFilters.subjectId)
      if (categoryFilters.chapterId) params.append('chapterId', categoryFilters.chapterId)
      if (categoryFilters.topicId) params.append('topicId', categoryFilters.topicId)

      const response = await fetch(`/api/student/practice?${params}`)

      if (!response.ok) {
        throw new Error('Failed to fetch practice data')
      }

      const data = await response.json()

      if (data.success) {
        setPracticeData(data.data.sessions || [])
        setStats(data.data.stats || stats)
      } else {
        throw new Error(data.message || 'Failed to fetch practice data')
      }
    } catch (error) {
      console.error('Error fetching practice data:', error)
      toast.error('Failed to load practice sessions')
    } finally {
      setLoading(false)
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'HARD':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const startPracticeSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/student/practice/${sessionId}/start`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

      if (!response.ok) {
        throw new Error('Failed to start practice session')
      }

      const data = await response.json()

      if (data.success) {
        toast.success('Practice session started!')
        // Navigate to quiz interface
        window.location.href = `/student/quiz/${sessionId}?attemptId=${data.data.attemptId}`
      } else {
        throw new Error(data.message || 'Failed to start practice session')
      }
    } catch (error) {
      console.error('Error starting practice session:', error)
      toast.error('Failed to start practice session')
    }
  }

  const resetProgress = async () => {
    if (!confirm('Are you sure you want to reset your practice progress? This action cannot be undone.')) {
      return
    }

    setResetting(true)
    try {
      const response = await fetch('/api/student/practice/reset', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

      if (!response.ok) {
        throw new Error('Failed to reset progress')
      }

      const data = await response.json()

      if (data.success) {
        toast.success('Practice progress reset successfully!')
        // Refresh the data
        await fetchPracticeData()
      } else {
        throw new Error(data.message || 'Failed to reset progress')
      }
    } catch (error) {
      console.error('Error resetting progress:', error)
      toast.error('Failed to reset progress')
    } finally {
      setResetting(false)
    }
  }

  const progressPercentage = (stats.todayCompleted / stats.todayTarget) * 100

  return (
    <div className="p-6 space-y-8 min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      {/* Header */}
      <div className="relative">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 rounded-2xl blur-3xl -z-10" />

        <div className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-white/20 dark:border-gray-800/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                  <Brain className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Daily Practice
                  </h1>
                  <p className="text-muted-foreground text-lg">
                    Build your skills with quick practice sessions
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={resetProgress}
                disabled={resetting}
                className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                {resetting ? 'Resetting...' : 'Reset Progress'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-orange-50/80 to-red-50/80 dark:from-orange-950/20 dark:to-red-950/20 backdrop-blur-sm hover:shadow-2xl hover:shadow-orange-500/20 transition-all duration-500 hover:-translate-y-1">
            {/* Gradient Border */}
            <div className="absolute inset-0 bg-gradient-to-r from-orange-400/30 via-red-400/30 to-pink-400/30 rounded-lg p-[1px]">
              <div className="h-full w-full bg-white/90 dark:bg-gray-900/90 rounded-lg" />
            </div>

            <CardContent className="relative z-10 pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Current Streak</p>
                  <p className="text-4xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                    {stats.currentStreak}
                  </p>
                  <p className="text-sm text-muted-foreground">days</p>
                </div>
                <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Zap className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="mt-4">
                <p className="text-xs text-muted-foreground">
                  Best: {stats.longestStreak} days
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-blue-50/80 to-indigo-50/80 dark:from-blue-950/20 dark:to-indigo-950/20 backdrop-blur-sm hover:shadow-2xl hover:shadow-blue-500/20 transition-all duration-500 hover:-translate-y-1">
            {/* Gradient Border */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400/30 via-indigo-400/30 to-purple-400/30 rounded-lg p-[1px]">
              <div className="h-full w-full bg-white/90 dark:bg-gray-900/90 rounded-lg" />
            </div>

            <CardContent className="relative z-10 pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Points</p>
                  <p className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    {stats.totalPoints.toLocaleString()}
                  </p>
                </div>
                <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Star className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="mt-4">
                <p className="text-xs text-muted-foreground">
                  Earned from practice sessions
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-green-50/80 to-emerald-50/80 dark:from-green-950/20 dark:to-emerald-950/20 backdrop-blur-sm hover:shadow-2xl hover:shadow-green-500/20 transition-all duration-500 hover:-translate-y-1">
            {/* Gradient Border */}
            <div className="absolute inset-0 bg-gradient-to-r from-green-400/30 via-emerald-400/30 to-teal-400/30 rounded-lg p-[1px]">
              <div className="h-full w-full bg-white/90 dark:bg-gray-900/90 rounded-lg" />
            </div>

            <CardContent className="relative z-10 pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Average Score</p>
                  <p className="text-4xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                    {stats.averageScore}%
                  </p>
                </div>
                <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="mt-4">
                <Progress value={stats.averageScore} className="h-3 bg-gray-200 dark:bg-gray-700" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-purple-50/80 to-pink-50/80 dark:from-purple-950/20 dark:to-pink-950/20 backdrop-blur-sm hover:shadow-2xl hover:shadow-purple-500/20 transition-all duration-500 hover:-translate-y-1">
            {/* Gradient Border */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-400/30 via-pink-400/30 to-rose-400/30 rounded-lg p-[1px]">
              <div className="h-full w-full bg-white/90 dark:bg-gray-900/90 rounded-lg" />
            </div>

            <CardContent className="relative z-10 pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Sessions</p>
                  <p className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    {stats.totalSessions}
                  </p>
                </div>
                <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Target className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="mt-4">
                <p className="text-xs text-muted-foreground">
                  Completed practice sessions
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Daily Progress */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Card className="border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg shadow-lg">
                <Calendar className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Today's Progress
              </span>
            </CardTitle>
            <CardDescription className="text-base">
              Complete {stats.todayTarget} practice sessions to maintain your streak
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex items-center justify-between text-sm font-medium">
                <span>Progress</span>
                <span className="text-lg font-bold">{stats.todayCompleted}/{stats.todayTarget} sessions</span>
              </div>
              <div className="space-y-2">
                <Progress value={progressPercentage} className="h-4 bg-gray-200 dark:bg-gray-700" />
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>0%</span>
                  <span>{Math.round(progressPercentage)}% complete</span>
                  <span>100%</span>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-sm font-medium text-green-700 dark:text-green-300">
                  {stats.todayCompleted} completed today
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Category Filter */}
      <Card className="border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm shadow-xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg shadow-lg">
              <BookOpen className="h-5 w-5 text-white" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
              Filter by Category
            </span>
          </CardTitle>
          <CardDescription className="text-base">
            Choose specific subjects, chapters, or topics for practice
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CategoryFilter
            selectedSubjectId={categoryFilters.subjectId}
            selectedChapterId={categoryFilters.chapterId}
            selectedTopicId={categoryFilters.topicId}
            onFilterChange={setCategoryFilters}
            userRole="STUDENT"
          />
        </CardContent>
      </Card>

      {/* Practice Sessions */}
      <div className="mt-6">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="pt-6">
                    <div className="space-y-3">
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-3 bg-muted rounded w-1/2"></div>
                      <div className="h-3 bg-muted rounded w-full"></div>
                      <div className="h-8 bg-muted rounded w-1/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : practiceData.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <Brain className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2">No practice sessions available</h3>
                  <p className="text-muted-foreground mb-6">
                    Check back later for new practice sessions in this category.
                  </p>
                  <Button asChild>
                    <Link href="/student/browse">
                      Browse Quizzes Instead
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {practiceData.map((session, index) => (
                <motion.div
                  key={session.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 * index }}
                >
                  <Card className={`hover:shadow-lg transition-shadow ${session.isCompleted ? 'bg-green-50 border-green-200' : ''}`}>
                    <CardContent className="pt-6">
                      <div className="space-y-4">
                        {/* Header */}
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg mb-1">{session.title}</h3>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {session.description}
                            </p>
                          </div>
                          {session.isCompleted && (
                            <CheckCircle className="h-5 w-5 text-green-600 mt-1" />
                          )}
                        </div>

                        {/* Metadata */}
                        <div className="flex items-center gap-2 flex-wrap">
                          <Badge variant="outline" className={getDifficultyColor(session.difficulty)}>
                            {session.difficulty}
                          </Badge>
                          {session.streak > 0 && (
                            <Badge variant="outline" className="bg-orange-100 text-orange-800">
                              <Zap className="h-3 w-3 mr-1" />
                              {session.streak} streak
                            </Badge>
                          )}
                        </div>

                        {/* Stats */}
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center gap-1">
                            <BookOpen className="h-4 w-4 text-muted-foreground" />
                            <span>{session.questionCount} questions</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span>{session.estimatedTime} min</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 text-muted-foreground" />
                            <span>{session.points} points</span>
                          </div>
                          {session.isCompleted && session.score && (
                            <div className="flex items-center gap-1">
                              <Award className="h-4 w-4 text-muted-foreground" />
                              <span>{session.score}% score</span>
                            </div>
                          )}
                        </div>

                        {/* Action */}
                        <div className="pt-2">
                          {session.isCompleted ? (
                            <div className="flex gap-2">
                              <Button variant="outline" className="flex-1" onClick={() => startPracticeSession(session.id)}>
                                <RotateCcw className="h-4 w-4 mr-2" />
                                Retry
                              </Button>
                              <Button variant="outline" size="sm">
                                <Award className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : (
                            <Button className="w-full" onClick={() => startPracticeSession(session.id)}>
                              <Play className="h-4 w-4 mr-2" />
                              Start Practice
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </div>
    </div>
  )
}
